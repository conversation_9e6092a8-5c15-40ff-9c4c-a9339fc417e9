# Database Configuration - You can use either the full URL or individual components
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/tutorial_db?schema=public"

# Individual database components (used if DATABASE_URL is not set)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=tutorial_db
DB_SCHEMA=public

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key-here"

# Google OAuth (Optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Application Settings
NODE_ENV=development
PORT=3000
API_URL=http://localhost:3000/api
