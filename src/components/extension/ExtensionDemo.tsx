"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Play,
  Pause,
  Volume2,
  Mic,
  Info,
  ChevronRight,
  ChevronLeft,
  X,
} from "lucide-react";
import HighlightingSystem from "./HighlightingSystem";
import { TutorialStep } from "./types";

interface ExtensionDemoProps {
  activeTab?: string;
  showDialog?: boolean;
}

const ExtensionDemo = ({
  activeTab = "webpage",
  showDialog = false,
}: ExtensionDemoProps) => {
  const [currentTab, setCurrentTab] = useState(activeTab);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [highlightedElement, setHighlightedElement] = useState<string | null>(
    "header",
  );
  const [isDialogOpen, setIsDialogOpen] = useState(showDialog);
  const [isVoiceActive, setIsVoiceActive] = useState(false);
  const [isTutorialActive, setIsTutorialActive] = useState(false);

  // Tutorial steps for the demo
  const tutorialSteps: TutorialStep[] = [
    {
      id: "step-1",
      selector: '[data-tutorial="header"]',
      title: "Website Header",
      description:
        "This is the navigation header of the website. It contains links to the main sections including Home, Features, Pricing, and Contact. You can click on any of these links to navigate to the corresponding page.",
      position: { placement: "bottom", align: "center" },
      highlightStyle: {
        borderColor: "#3b82f6",
        borderWidth: 2,
        shadowColor: "rgba(59, 130, 246, 0.5)",
        shadowBlur: 20,
        pulseAnimation: true,
      },
      action: { type: "hover" },
    },
    {
      id: "step-2",
      selector: '[data-tutorial="content"]',
      title: "Main Content Area",
      description:
        "This is the main content area where the primary information is displayed. It contains articles, product information, or other key content that users come to see.",
      position: { placement: "right", align: "start" },
      highlightStyle: {
        borderColor: "#10b981",
        borderWidth: 2,
        shadowColor: "rgba(16, 185, 129, 0.5)",
        shadowBlur: 20,
        pulseAnimation: true,
      },
      action: { type: "click" },
    },
    {
      id: "step-3",
      selector: '[data-tutorial="sidebar"]',
      title: "Navigation Sidebar",
      description:
        "The sidebar provides additional navigation options and quick access to related content. Use these links to explore different sections of the website.",
      position: { placement: "left", align: "center" },
      highlightStyle: {
        borderColor: "#f59e0b",
        borderWidth: 2,
        shadowColor: "rgba(245, 158, 11, 0.5)",
        shadowBlur: 20,
        pulseAnimation: true,
      },
      action: { type: "hover" },
    },
    {
      id: "step-4",
      selector: '[data-tutorial="footer"]',
      title: "Website Footer",
      description:
        "The footer contains important links like privacy policy, terms of service, and contact information. It also includes copyright information and social media links.",
      position: { placement: "top", align: "center" },
      highlightStyle: {
        borderColor: "#8b5cf6",
        borderWidth: 2,
        shadowColor: "rgba(139, 92, 246, 0.5)",
        shadowBlur: 20,
        pulseAnimation: true,
      },
      action: { type: "click" },
    },
  ];

  // Simulate AI response streaming
  const [aiResponse, setAiResponse] = useState("");
  const fullResponse =
    "This is the navigation header of the website. It contains links to the main sections including Home, Features, Pricing, and Contact. You can click on any of these links to navigate to the corresponding page.";

  useEffect(() => {
    if (isPlaying && aiResponse.length < fullResponse.length) {
      const timer = setTimeout(() => {
        setAiResponse(fullResponse.substring(0, aiResponse.length + 3));
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [isPlaying, aiResponse]);

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
    if (!isPlaying && aiResponse.length === fullResponse.length) {
      setAiResponse("");
    }
  };

  const handleNextStep = () => {
    setCurrentStep((prev) => Math.min(prev + 1, 4));
    setHighlightedElement(
      currentStep === 1 ? "content" : currentStep === 2 ? "sidebar" : "footer",
    );
    setAiResponse("");
  };

  const handlePrevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
    setHighlightedElement(
      currentStep === 3 ? "sidebar" : currentStep === 2 ? "header" : "header",
    );
    setAiResponse("");
  };

  const handleElementClick = (element: string) => {
    setHighlightedElement(element);
    setAiResponse("");
    setIsPlaying(true);
  };

  const handleVoiceToggle = () => {
    setIsVoiceActive(!isVoiceActive);
  };

  return (
    <div className="w-full max-w-4xl mx-auto bg-background">
      <Card className="shadow-lg border-2 border-border">
        <CardContent className="p-0 overflow-hidden">
          <div className="bg-muted p-2 flex items-center justify-between border-b border-border">
            <div className="flex items-center space-x-2">
              <div className="h-3 w-3 rounded-full bg-red-500"></div>
              <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
              <div className="h-3 w-3 rounded-full bg-green-500"></div>
            </div>
            <div className="flex items-center bg-background/50 rounded-md px-3 py-1 text-xs w-1/2">
              https://example.com/dashboard
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="bg-primary/10">
                AI Tutor Active
              </Badge>
            </div>
          </div>

          <Tabs
            value={currentTab}
            onValueChange={setCurrentTab}
            className="w-full"
          >
            <TabsList className="w-full justify-start px-4 py-2 bg-muted/50">
              <TabsTrigger value="webpage">Webpage</TabsTrigger>
              <TabsTrigger value="tutorial">Tutorial Mode</TabsTrigger>
              <TabsTrigger value="settings">Extension Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="webpage" className="m-0">
              <div className="relative h-[400px] overflow-hidden">
                {/* Simulated webpage content */}
                <div className="p-4 h-full">
                  <div
                    className={`p-4 mb-4 border ${highlightedElement === "header" ? "border-primary ring-2 ring-primary" : "border-border"}`}
                    onClick={() => handleElementClick("header")}
                    data-tutorial="header"
                  >
                    <div className="flex justify-between items-center">
                      <div className="font-bold text-lg">Website Logo</div>
                      <div className="flex space-x-4">
                        <span>Home</span>
                        <span>Features</span>
                        <span>Pricing</span>
                        <span>Contact</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-4 h-[250px]">
                    <div
                      className={`w-2/3 p-4 border ${highlightedElement === "content" ? "border-primary ring-2 ring-primary" : "border-border"}`}
                      onClick={() => handleElementClick("content")}
                      data-tutorial="content"
                    >
                      <h2 className="text-lg font-semibold mb-2">
                        Main Content
                      </h2>
                      <p className="text-sm text-muted-foreground">
                        This is the main content area of the website. It
                        contains information about the product or service being
                        offered.
                      </p>
                      <div className="mt-4 grid grid-cols-2 gap-2">
                        <div className="h-20 bg-muted/50 rounded-md"></div>
                        <div className="h-20 bg-muted/50 rounded-md"></div>
                        <div className="h-20 bg-muted/50 rounded-md"></div>
                        <div className="h-20 bg-muted/50 rounded-md"></div>
                      </div>
                    </div>

                    <div
                      className={`w-1/3 p-4 border ${highlightedElement === "sidebar" ? "border-primary ring-2 ring-primary" : "border-border"}`}
                      onClick={() => handleElementClick("sidebar")}
                      data-tutorial="sidebar"
                    >
                      <h3 className="text-md font-semibold mb-2">Sidebar</h3>
                      <ul className="text-sm space-y-2">
                        <li className="p-2 bg-muted/30 rounded">Menu Item 1</li>
                        <li className="p-2 bg-muted/30 rounded">Menu Item 2</li>
                        <li className="p-2 bg-muted/30 rounded">Menu Item 3</li>
                        <li className="p-2 bg-muted/30 rounded">Menu Item 4</li>
                      </ul>
                    </div>
                  </div>

                  <div
                    className={`mt-4 p-4 border ${highlightedElement === "footer" ? "border-primary ring-2 ring-primary" : "border-border"}`}
                    onClick={() => handleElementClick("footer")}
                    data-tutorial="footer"
                  >
                    <div className="flex justify-between items-center text-sm">
                      <div>© 2023 Company Name</div>
                      <div className="flex space-x-4">
                        <span>Privacy</span>
                        <span>Terms</span>
                        <span>Contact</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* AI Tooltip overlay */}
                {highlightedElement && (
                  <div className="absolute bottom-4 right-4 w-64 bg-background border border-border rounded-lg shadow-lg p-3 z-10">
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex items-center">
                        <Info size={14} className="text-primary mr-2" />
                        <span className="text-xs font-medium">
                          AI Explanation
                        </span>
                      </div>
                      <div className="flex space-x-1">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={handlePlayPause}
                              >
                                {isPlaying ? (
                                  <Pause size={14} />
                                ) : (
                                  <Play size={14} />
                                )}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{isPlaying ? "Pause" : "Play"} explanation</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className={`h-6 w-6 p-0 ${isVoiceActive ? "text-primary" : ""}`}
                                onClick={handleVoiceToggle}
                              >
                                <Volume2 size={14} />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {isVoiceActive ? "Disable" : "Enable"} voice
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                    <div className="text-xs h-20 overflow-y-auto">
                      {aiResponse || "Click play to hear explanation..."}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="tutorial" className="m-0">
              <div className="relative h-[400px] overflow-hidden bg-muted/20 flex flex-col">
                <div className="p-4 flex-1">
                  <div className="bg-background border border-border rounded-lg p-4 mb-4 shadow-sm">
                    <h3 className="text-lg font-medium mb-2">
                      Website Navigation Tutorial
                    </h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Step {currentStep} of 4:{" "}
                      {currentStep === 1
                        ? "Understanding the header"
                        : currentStep === 2
                          ? "Exploring the main content"
                          : currentStep === 3
                            ? "Using the sidebar"
                            : "Footer information"}
                    </p>

                    <div className="h-[200px] border border-dashed border-border rounded-md flex items-center justify-center">
                      <div className="text-center text-muted-foreground">
                        <div className="mb-2">
                          Tutorial content for step {currentStep}
                        </div>
                        <div className="space-y-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsDialogOpen(true)}
                          >
                            View Interactive Demo
                          </Button>
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() => setIsTutorialActive(true)}
                            className="ml-2"
                          >
                            Start Advanced Tutorial
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border-t border-border p-4 bg-background flex justify-between items-center">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePrevStep}
                    disabled={currentStep === 1}
                  >
                    <ChevronLeft size={16} className="mr-1" /> Previous
                  </Button>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 rounded-full"
                    >
                      <Mic size={14} />
                    </Button>
                    <span className="text-xs text-muted-foreground">
                      "Say 'Next' or 'Previous'"
                    </span>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleNextStep}
                    disabled={currentStep === 4}
                  >
                    Next <ChevronRight size={16} className="ml-1" />
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="m-0">
              <div className="h-[400px] p-6 bg-muted/20">
                <h3 className="text-lg font-medium mb-4">Extension Settings</h3>

                <div className="space-y-4">
                  <div className="bg-background border border-border rounded-lg p-4 shadow-sm">
                    <h4 className="text-sm font-medium mb-2">AI Provider</h4>
                    <div className="grid grid-cols-3 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="justify-start"
                      >
                        <img
                          src="https://upload.wikimedia.org/wikipedia/commons/0/04/ChatGPT_logo.svg"
                          alt="OpenAI"
                          className="h-4 w-4 mr-2"
                        />
                        OpenAI
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="justify-start"
                      >
                        <img
                          src="https://upload.wikimedia.org/wikipedia/commons/1/10/Anthropic_logo.svg"
                          alt="Anthropic"
                          className="h-4 w-4 mr-2"
                        />
                        Anthropic
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="justify-start"
                      >
                        <img
                          src="https://www.gstatic.com/lamda/images/gemini_sparkle_v002_576px.gif"
                          alt="Google AI"
                          className="h-4 w-4 mr-2"
                        />
                        Google AI
                      </Button>
                    </div>
                  </div>

                  <div className="bg-background border border-border rounded-lg p-4 shadow-sm">
                    <h4 className="text-sm font-medium mb-2">Voice Settings</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Voice Provider</span>
                        <Badge>ElevenLabs</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Voice Speed</span>
                        <div className="w-32 h-1 bg-muted rounded-full relative">
                          <div
                            className="absolute h-3 w-3 bg-primary rounded-full top-1/2 -translate-y-1/2"
                            style={{ left: "60%" }}
                          ></div>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Language</span>
                        <Badge variant="outline">English (US)</Badge>
                      </div>
                    </div>
                  </div>

                  <div className="bg-background border border-border rounded-lg p-4 shadow-sm">
                    <h4 className="text-sm font-medium mb-2">
                      Tutorial Preferences
                    </h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Auto-start tutorials</span>
                        <div className="h-4 w-8 bg-primary rounded-full relative">
                          <div className="absolute h-3 w-3 bg-white rounded-full top-1/2 -translate-y-1/2 right-0.5"></div>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Voice commands</span>
                        <div className="h-4 w-8 bg-primary rounded-full relative">
                          <div className="absolute h-3 w-3 bg-white rounded-full top-1/2 -translate-y-1/2 right-0.5"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Interactive Tutorial</DialogTitle>
            <DialogDescription>
              Follow along with this interactive guide to learn how to use the
              website.
            </DialogDescription>
          </DialogHeader>
          <div className="p-4 border border-border rounded-md bg-muted/20">
            <div className="flex items-center justify-between mb-4">
              <Badge variant="outline" className="bg-primary/10">
                Step {currentStep} of 4
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsDialogOpen(false)}
              >
                <X size={16} />
              </Button>
            </div>
            <div className="space-y-4">
              <p className="text-sm">
                {currentStep === 1
                  ? "The header contains navigation links to help you move around the website."
                  : currentStep === 2
                    ? "The main content area displays the primary information for the current page."
                    : currentStep === 3
                      ? "The sidebar provides additional navigation options and related links."
                      : "The footer contains important links like privacy policy and contact information."}
              </p>
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePrevStep}
                  disabled={currentStep === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleNextStep}
                  disabled={currentStep === 4}
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Advanced Highlighting System */}
      <HighlightingSystem
        steps={tutorialSteps}
        isActive={isTutorialActive}
        config={{
          theme: "auto",
          animationDuration: 300,
          showProgress: true,
          allowSkip: true,
          keyboardNavigation: true,
          autoAdvance: false,
        }}
        onStepChange={(step, stepData) => {
          console.log("Step changed:", step, stepData);
        }}
        onComplete={() => {
          console.log("Tutorial completed");
          setIsTutorialActive(false);
        }}
        onSkip={() => {
          console.log("Tutorial skipped");
          setIsTutorialActive(false);
        }}
        onClose={() => {
          console.log("Tutorial closed");
          setIsTutorialActive(false);
        }}
      />
    </div>
  );
};

export default ExtensionDemo;
