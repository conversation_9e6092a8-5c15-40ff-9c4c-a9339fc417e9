import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { AIProvider } from "@/models/AIUsage";

// This would integrate with actual AI providers
const AI_PROVIDERS = {
  openai: {
    baseUrl: "https://api.openai.com/v1",
    model: "gpt-4-turbo-preview",
  },
  anthropic: {
    baseUrl: "https://api.anthropic.com/v1",
    model: "claude-3-sonnet-20240229",
  },
  google: {
    baseUrl: "https://generativelanguage.googleapis.com/v1beta",
    model: "gemini-pro",
  },
};

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true, settings: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const body = await request.json();
    const {
      element,
      context,
      question,
      language = "en",
      provider = (user.settings as any)?.aiProvider || "openai",
    } = body;

    if (!element && !question) {
      return NextResponse.json(
        { error: "Element or question is required" },
        { status: 400 },
      );
    }

    // Create streaming response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        try {
          const startTime = Date.now();

          // Simulate AI explanation generation
          const explanation = await generateExplanation({
            element,
            context,
            question,
            language,
            provider: provider as AIProvider,
          });

          // Stream the response
          const words = explanation.split(" ");
          for (let i = 0; i < words.length; i++) {
            const chunk = words.slice(0, i + 1).join(" ");
            controller.enqueue(
              encoder.encode(
                `data: ${JSON.stringify({ text: chunk, done: false })}\n\n`,
              ),
            );
            await new Promise((resolve) => setTimeout(resolve, 50)); // Simulate streaming delay
          }

          // Final chunk
          controller.enqueue(
            encoder.encode(
              `data: ${JSON.stringify({ text: explanation, done: true })}\n\n`,
            ),
          );

          const endTime = Date.now();
          const duration = endTime - startTime;

          // Track AI usage
          await prisma.aIUsage.create({
            data: {
              userId: user.id,
              provider: provider as AIProvider,
              model:
                AI_PROVIDERS[provider as keyof typeof AI_PROVIDERS]?.model ||
                "unknown",
              tokens: Math.ceil(explanation.length / 4), // Rough token estimation
              cost: calculateCost(provider as AIProvider, explanation.length),
              requestType: "explanation",
              metadata: {
                duration,
                success: true,
                language,
                prompt: question || `Explain: ${element}`,
                response: explanation,
              },
            },
          });

          // Track analytics
          await prisma.analytics.create({
            data: {
              userId: user.id,
              action: "ai_explanation_requested",
              metadata: {
                element,
                language,
                aiProvider: provider,
                duration,
                sessionId: request.headers.get("x-session-id") || undefined,
              },
            },
          });

          controller.close();
        } catch (error) {
          console.error("Error generating explanation:", error);
          controller.enqueue(
            encoder.encode(
              `data: ${JSON.stringify({ error: "Failed to generate explanation", done: true })}\n\n`,
            ),
          );
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    });
  } catch (error) {
    console.error("Error in AI explain endpoint:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

async function generateExplanation(params: {
  element?: string;
  context?: string;
  question?: string;
  language: string;
  provider: AIProvider;
}): Promise<string> {
  // This is a mock implementation
  // In a real app, you would integrate with actual AI providers

  const { element, context, question, language } = params;

  if (question) {
    return `Based on the current webpage context, here's an explanation for your question "${question}": This element serves a specific purpose in the user interface and helps users navigate or interact with the content effectively. The design and placement are optimized for user experience and accessibility.`;
  }

  if (element) {
    return `This ${element} element is an important part of the webpage interface. It provides functionality that allows users to interact with the content in a meaningful way. The element is positioned strategically to enhance user experience and follows modern web design principles for optimal usability and accessibility.`;
  }

  return "I can help explain any element on this webpage. Please click on an element or ask a specific question.";
}

function calculateCost(provider: AIProvider, textLength: number): number {
  // Mock cost calculation based on provider and text length
  const baseCosts = {
    openai: 0.002,
    anthropic: 0.003,
    google: 0.001,
    openrouter: 0.002,
    groq: 0.001,
  };

  const tokens = Math.ceil(textLength / 4);
  return (tokens / 1000) * baseCosts[provider];
}
