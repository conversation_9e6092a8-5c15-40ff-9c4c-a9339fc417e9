import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { AIProvider } from "@/models/AIUsage";
// Use fallback implementation that works without external AG-UI packages
import { getAgUIServerFallback } from "@/lib/ag-ui-fallback";
import type { AgentEvent } from '@/lib/ag-ui-types';

// This would integrate with actual AI providers
const AI_PROVIDERS = {
  openai: {
    baseUrl: "https://api.openai.com/v1",
    model: "gpt-4-turbo-preview",
  },
  anthropic: {
    baseUrl: "https://api.anthropic.com/v1",
    model: "claude-3-sonnet-20240229",
  },
  google: {
    baseUrl: "https://generativelanguage.googleapis.com/v1beta",
    model: "gemini-pro",
  },
};

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true, settings: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const body = await request.json();
    const {
      element,
      context,
      question,
      language = "en",
      provider = (user.settings as any)?.aiProvider || "openai",
    } = body;

    if (!element && !question) {
      return NextResponse.json(
        { error: "Element or question is required" },
        { status: 400 },
      );
    }

    // Create AG-UI streaming response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        try {
          const startTime = Date.now();
          const agUIServer = getAgUIServerFallback();
          const runId = `run_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          const threadId = `thread_${Date.now()}`;

          let fullResponse = '';
          let tokenCount = 0;

          // Set up AG-UI event listener for this stream
          const eventListener = (event: AgentEvent) => {
            try {
              switch (event.type) {
                case 'TextMessageContent':
                  const contentEvent = event as any;
                  fullResponse += contentEvent.delta;
                  tokenCount += Math.ceil(contentEvent.delta.length / 4);

                  controller.enqueue(
                    encoder.encode(
                      `data: ${JSON.stringify({ text: fullResponse, done: false })}\n\n`,
                    ),
                  );
                  break;

                case 'TextMessageEnd':
                  controller.enqueue(
                    encoder.encode(
                      `data: ${JSON.stringify({ text: fullResponse, done: true })}\n\n`,
                    ),
                  );
                  break;

                case 'RunError':
                  const errorEvent = event as any;
                  controller.enqueue(
                    encoder.encode(
                      `data: ${JSON.stringify({ error: errorEvent.message, done: true })}\n\n`,
                    ),
                  );
                  break;
              }
            } catch (streamError) {
              console.error('Error processing AG-UI event:', streamError);
            }
          };

          agUIServer.onEvent(eventListener);

          // Trigger AI explanation via AG-UI
          await agUIServer.explainElement(
            element || 'body',
            question,
            context?.url || 'unknown',
            runId,
            threadId
          );

          // Wait for completion or timeout
          const timeout = setTimeout(() => {
            controller.enqueue(
              encoder.encode(
                `data: ${JSON.stringify({ error: "Request timeout", done: true })}\n\n`,
              ),
            );
            controller.close();
          }, 30000); // 30 second timeout

          // Listen for run completion
          const completionListener = async (event: AgentEvent) => {
            if (event.type === 'RunFinished' || event.type === 'RunError') {
              clearTimeout(timeout);

              const endTime = Date.now();
              const duration = endTime - startTime;

              // Track AI usage
              await prisma.aIUsage.create({
                data: {
                  userId: user.id,
                  provider: provider as AIProvider,
                  model:
                    AI_PROVIDERS[provider as keyof typeof AI_PROVIDERS]?.model ||
                    "unknown",
                  tokens: tokenCount,
                  cost: calculateCost(provider as AIProvider, fullResponse.length),
                  requestType: "explanation",
                  metadata: {
                    duration,
                    success: event.type === 'RunFinished',
                    language,
                    prompt: question || `Explain: ${element}`,
                    response: fullResponse,
                    runId,
                    threadId,
                  },
                },
              });

              // Track analytics
              await prisma.analytics.create({
                data: {
                  userId: user.id,
                  action: "ai_explanation_requested",
                  metadata: {
                    element,
                    language,
                    aiProvider: provider,
                    duration,
                    success: event.type === 'RunFinished',
                    runId,
                    threadId,
                    sessionId: request.headers.get("x-session-id") || undefined,
                  },
                },
              });

              controller.close();
            }
          };

          agUIServer.onEvent(completionListener);

        } catch (error) {
          console.error("Error generating explanation:", error);
          controller.enqueue(
            encoder.encode(
              `data: ${JSON.stringify({ error: "Failed to generate explanation", done: true })}\n\n`,
            ),
          );
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    });
  } catch (error) {
    console.error("Error in AI explain endpoint:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// Mock function removed - now using AG-UI real AI integration

function calculateCost(provider: AIProvider, textLength: number): number {
  // Mock cost calculation based on provider and text length
  const baseCosts = {
    openai: 0.002,
    anthropic: 0.003,
    google: 0.001,
    openrouter: 0.002,
    groq: 0.001,
  };

  const tokens = Math.ceil(textLength / 4);
  return (tokens / 1000) * baseCosts[provider];
}
