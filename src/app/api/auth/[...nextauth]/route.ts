import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { prisma } from "@/lib/database";
import { defaultUserSettings, UserSettings } from "@/models/User";
import bcrypt from "bcryptjs";
import { DATABASE_CONFIG } from "@/lib/config";

// Check for DATABASE_URL before initializing NextAuth
if (!DATABASE_CONFIG.URL) {
  console.error("Error: DATABASE_URL environment variable is not set. Authentication will fall back to credentials only.");
  console.error("Please check your .env file and restart the application.");
}

// Create a custom adapter that wraps the PrismaAdapter with error handling
const createSafeAdapter = () => {
  // Create a base adapter
  const adapter = PrismaAdapter(prisma);
  
  // Wrap each method with try-catch
  return {
    ...adapter,
    createUser: async (data: any) => {
      try {
        return await adapter.createUser?.(data) || {
          id: `temp_${Date.now()}`,
          email: data.email,
          emailVerified: null,
          ...data
        };
      } catch (error) {
        console.error("Error creating user:", error);
        // Return minimal user object
        return {
          id: `temp_${Date.now()}`,
          email: data.email,
          emailVerified: null,
          ...data
        };
      }
    },
    getUser: async (id: string) => {
      try {
        return await adapter.getUser?.(id) || null;
      } catch (error) {
        console.error("Error getting user:", error);
        return null;
      }
    },
    getUserByEmail: async (email: string) => {
      try {
        return await adapter.getUserByEmail?.(email) || null;
      } catch (error) {
        console.error("Error getting user by email:", error);
        return null;
      }
    },
    // Add other methods as needed
  };
};

const handler = NextAuth({
  adapter: createSafeAdapter(),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || "demo-client-id",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "demo-client-secret",
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const user = await prisma.user.findUnique({
            where: { email: credentials.email },
          });

          if (!user || !user.password) {
            return null;
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password,
          );

          if (!isPasswordValid) {
            return null;
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
          };
        } catch (error) {
          console.error("Error during authentication:", error);
          // For demo or development, you might want to allow a fallback user
          if (process.env.NODE_ENV !== "production" && credentials.email === "<EMAIL>" && credentials.password === "admin123") {
            return {
              id: "demo-admin",
              email: "<EMAIL>",
              name: "Demo Admin",
              role: "ADMIN"
            };
          }
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async session({ session, user }) {
      if (session?.user) {
        try {
          // Get user settings from database
          const userData = await prisma.user.findUnique({
            where: { id: user.id },
            select: { role: true, subscription: true, settings: true },
          });

          (session.user as any).role = userData?.role || "USER";
          (session.user as any).subscription = userData?.subscription || "free";
          (session.user as any).settings =
            userData?.settings || (defaultUserSettings as UserSettings);
          (session.user as any).id = user.id;
        } catch (error) {
          console.error("Error fetching user data for session:", error);
          // Provide default values if database query fails
          (session.user as any).role = "USER";
          (session.user as any).subscription = "free";
          (session.user as any).settings = defaultUserSettings as UserSettings;
          (session.user as any).id = user.id;
        }
      }
      return session;
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === "google") {
        try {
          // Check if user exists
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email! },
          });

          if (!existingUser) {
            // Create new user with default settings
            await prisma.user.create({
              data: {
                email: user.email!,
                name: user.name,
                image: user.image,
                settings: defaultUserSettings as any,
              },
            });
          }
          return true;
        } catch (error) {
          console.error("Error during sign in:", error);
          // Allow sign-in even if database operations fail
          return true;
        }
      }
      return true;
    },
    async redirect({ url, baseUrl }) {
      // Redirect to dashboard after successful login
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      else if (new URL(url).origin === baseUrl) return url;
      return `${baseUrl}/dashboard`;
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt", // Use JWT instead of database sessions for better reliability
  },
});

export { handler as GET, handler as POST };
