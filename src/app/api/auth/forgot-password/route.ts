import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database";
import { z } from "zod";
import crypto from "crypto";
import { AUTH_CONFIG } from "@/lib/constants";

const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validationResult = forgotPasswordSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.flatten().fieldErrors },
        { status: 400 }
      );
    }

    const { email } = validationResult.data;

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      // For security reasons, don't reveal that the user doesn't exist
      // Return success even though no email will be sent
      return NextResponse.json({
        success: true,
        message: "If your email is registered, you will receive a reset link",
      });
    }

    // Generate a secure random token
    const token = crypto.randomBytes(32).toString("hex");
    const expires = new Date(Date.now() + 3600000); // 1 hour from now

    // Store the token in the database
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token,
        expires,
      },
    });

    // In a real implementation, you would send an email with the reset link
    // For this implementation, we'll just return the token for demonstration
    const resetLink = `${process.env.NEXTAUTH_URL}/auth/reset-password?token=${token}`;

    // Log the password reset request 
    await prisma.auditLog.create({
      data: {
        userId: user.id,
        action: "password_reset_requested",
        resource: "user",
        resourceId: user.id,
        metadata: {
          timestamp: new Date(),
          ipAddress: request.headers.get("x-forwarded-for") || "unknown",
        },
      },
    });

    // In development, return the reset link for testing
    if (process.env.NODE_ENV === "development") {
      return NextResponse.json({
        success: true,
        message: "Password reset link has been sent to your email",
        devInfo: { resetLink },
      });
    }

    return NextResponse.json({
      success: true,
      message: "Password reset link has been sent to your email",
    });
  } catch (error) {
    console.error("Error requesting password reset:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 