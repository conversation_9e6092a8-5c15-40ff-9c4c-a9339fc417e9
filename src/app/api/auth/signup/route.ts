import { NextRequest, NextResponse } from "next/server";
import { hash } from "bcryptjs";
import { prisma } from "@/lib/database";
import { defaultUserSettings } from "@/models/User";
import { z } from "zod";
import { rateLimit } from "@/lib/rate-limit";

// Input validation schema
const signupSchema = z.object({
  name: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(50, "Name must be less than 50 characters")
    .regex(/^[a-zA-Z\s]+$/, "Name can only contain letters and spaces")
    .trim(),
  email: z
    .string()
    .email("Please enter a valid email address")
    .toLowerCase()
    .trim(),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .max(128, "Password must be less than 128 characters")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Password must contain at least one uppercase letter, one lowercase letter, and one number",
    ),
});

// Rate limiting: 5 attempts per 15 minutes per IP
const limiter = rateLimit({
  interval: 15 * 60 * 1000, // 15 minutes
  uniqueTokenPerInterval: 500, // Max 500 unique IPs per interval
});

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const ip =
      request.ip ?? request.headers.get("x-forwarded-for") ?? "unknown";
    const { success, limit, reset, remaining } = await limiter.check(5, ip);

    if (!success) {
      return NextResponse.json(
        {
          message: "Too many registration attempts. Please try again later.",
          retryAfter: Math.round((reset - Date.now()) / 1000),
        },
        {
          status: 429,
          headers: {
            "X-RateLimit-Limit": limit.toString(),
            "X-RateLimit-Remaining": remaining.toString(),
            "X-RateLimit-Reset": reset.toString(),
          },
        },
      );
    }

    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        { message: "Invalid JSON in request body" },
        { status: 400 },
      );
    }

    // Validate input data
    const validationResult = signupSchema.safeParse(body);
    if (!validationResult.success) {
      const errors = Object.entries(
        validationResult.error.flatten().fieldErrors,
      ).map(([field, error]) => ({
        field,
        message: error[0] || "",
      })); 

      return NextResponse.json(
        {
          message: "Validation failed",
          errors,
        },
        { status: 400 },
      );
    }

    const { name, email, password } = validationResult.data;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
      select: { id: true },
    });

    if (existingUser) {
      return NextResponse.json(
        { message: "An account with this email already exists" },
        { status: 409 },
      );
    }

    // Hash password with higher cost for production
    const saltRounds = process.env.NODE_ENV === "production" ? 14 : 12;
    const hashedPassword = await hash(password, saltRounds);

    // Create user in transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          name,
          email,
          password: hashedPassword,
          settings: defaultUserSettings as any,
          role: "USER", // Explicitly set default role
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
        },
      });

      // Track user registration analytics
      await tx.analytics.create({
        data: {
          userId: user.id,
          action: "user_registered",
          metadata: {
            registrationMethod: "email",
            timestamp: new Date().toISOString(),
            userAgent: request.headers.get("user-agent") || "unknown",
            ip: ip !== "unknown" ? ip : undefined,
          },
        },
      });

      return user;
    });

    // Log successful registration (without sensitive data)
    console.log(`New user registered: ${result.email} (ID: ${result.id})`);

    return NextResponse.json(
      {
        message: "Account created successfully",
        user: {
          id: result.id,
          name: result.name,
          email: result.email,
          role: result.role,
        },
      },
      { status: 201 },
    );
  } catch (error) {
    // Log error details for debugging (in production, use proper logging service)
    console.error("Registration error:", {
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
    });

    // Return generic error message to avoid information leakage
    return NextResponse.json(
      {
        message:
          "An error occurred while creating your account. Please try again.",
      },
      { status: 500 },
    );
  }
}
