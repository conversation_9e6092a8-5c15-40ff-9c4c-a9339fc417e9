import { PrismaClient } from "@prisma/client";
import { DATABASE_CONFIG, buildDatabaseUrl } from "./config";

// Check if DATABASE_URL is set
if (!DATABASE_CONFIG.URL) {
  console.error(
    "Error: DATABASE_URL environment variable is not set. Please check your .env file."
  );
  // In development, provide more helpful information
  if (process.env.NODE_ENV !== "production") {
    console.error(
      "Make sure you have created a .env file in the root directory with DATABASE_URL defined."
    );
    console.error(
      "Example: DATABASE_URL=\"postgresql://username:password@localhost:5432/tutorial_db?schema=public\""
    );
    console.error(
      "Or set individual components: DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME"
    );
  }
}

// Define global type for Prisma client
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Create a mock PrismaClient for when database is not available
class MockPrismaClient {
  $connect() { return Promise.resolve(); }
  $disconnect() { return Promise.resolve(); }
  $queryRaw() { return Promise.resolve([]); }
  $executeRaw() { return Promise.resolve({}); }
  user = {
    findUnique: () => Promise.resolve(null),
    findFirst: () => Promise.resolve(null),
    create: () => Promise.resolve(null),
    update: () => Promise.resolve(null),
    count: () => Promise.resolve(0),
  };
  // Add other models as needed
}

// Initialize Prisma client with appropriate logging based on environment
// Use try-catch to handle initialization errors
let prismaInstance;
try {
  prismaInstance = new PrismaClient({
    log: process.env.NODE_ENV === "development" ? ["query", "error", "warn"] : ["error"],
    datasources: {
      db: {
        url: buildDatabaseUrl(),
      },
    },
  });
} catch (error) {
  console.error("Failed to initialize Prisma client:", error);
  console.error("Using mock client instead. Some functionality will be limited.");
  prismaInstance = new MockPrismaClient() as unknown as PrismaClient;
}

export const prisma = globalForPrisma.prisma ?? prismaInstance;

// Keep Prisma client instance in development to avoid too many connections
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

/**
 * Database connection helper with error handling
 * @returns Promise that resolves when connected or rejects with error
 */
export async function connectToDatabase() {
  try {
    if (!DATABASE_CONFIG.URL) {
      throw new Error("DATABASE_URL environment variable is not set");
    }
    
    await prisma.$connect();
    console.log("Connected to database successfully");
    return true;
  } catch (error) {
    console.error("Failed to connect to database:", error);
    
    // Provide more helpful error messages based on error type
    if (error instanceof Error) {
      if (error.message.includes("connect ECONNREFUSED")) {
        console.error(`Database server is not running or not accessible at ${DATABASE_CONFIG.HOST}:${DATABASE_CONFIG.PORT}`);
      } else if (error.message.includes("authentication failed")) {
        console.error("Database authentication failed - check username and password");
      } else if (error.message.includes("does not exist")) {
        console.error(`Database '${DATABASE_CONFIG.NAME}' does not exist - you may need to create it first`);
      }
    }
    
    return false;
  }
}

/**
 * Graceful shutdown of database connection
 */
export async function disconnectFromDatabase() {
  try {
    await prisma.$disconnect();
    console.log("Disconnected from database");
  } catch (error) {
    console.error("Error disconnecting from database:", error);
  }
}

/**
 * Utility function to check database connection
 * @returns Promise that resolves to boolean indicating connection status
 */
export async function isDatabaseConnected(): Promise<boolean> {
  try {
    // Execute a simple query to check connection
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error("Database connection check failed:", error);
    return false;
  }
}
