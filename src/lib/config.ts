/**
 * Centralized configuration settings
 * All environment variables and configuration settings should be accessed through this file
 */

// Database configuration
export const DATABASE_CONFIG = {
  URL: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/tutorial_db?schema=public',
  HOST: process.env.DB_HOST || 'localhost',
  PORT: parseInt(process.env.DB_PORT || '5432', 10),
  USER: process.env.DB_USER || 'postgres',
  PASSWORD: process.env.DB_PASSWORD || 'postgres',
  NAME: process.env.DB_NAME || 'tutorial_db',
  SCHEMA: process.env.DB_SCHEMA || 'public',
};

// Auth configuration
export const AUTH_CONFIG = {
  NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'http://localhost:3000',
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET || 'your-nextauth-secret-key-here',
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID || '',
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET || '',
};

// Application settings
export const APP_CONFIG = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: parseInt(process.env.PORT || '3000', 10),
  API_URL: process.env.API_URL || 'http://localhost:3000/api',
};

// Helper function to build DATABASE_URL from individual components
export function buildDatabaseUrl(): string {
  if (process.env.DATABASE_URL) {
    return process.env.DATABASE_URL;
  }
  
  return `postgresql://${DATABASE_CONFIG.USER}:${DATABASE_CONFIG.PASSWORD}@${DATABASE_CONFIG.HOST}:${DATABASE_CONFIG.PORT}/${DATABASE_CONFIG.NAME}?schema=${DATABASE_CONFIG.SCHEMA}`;
} 