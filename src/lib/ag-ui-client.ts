/**
 * AG-UI Client Configuration for TutorAI
 * Handles real-time communication between AI agents and frontend
 */

import { 
  AgentEvent, 
  RunStartedEvent, 
  RunFinishedEvent, 
  RunErrorEvent,
  TextMessageStartEvent,
  TextMessageContentEvent,
  TextMessageEndEvent,
  ToolCallStartEvent,
  ToolCallArgsEvent,
  ToolCallEndEvent,
  ToolCallResultEvent,
  StateSnapshotEvent,
  StateDeltaEvent,
  StepStartedEvent,
  StepFinishedEvent
} from '@ag-ui/core';

export interface AgUIClientConfig {
  baseUrl: string;
  apiKey?: string;
  transport: 'sse' | 'websocket' | 'polling';
  reconnectAttempts: number;
  reconnectDelay: number;
}

export interface TutorAIState {
  currentStep: number;
  totalSteps: number;
  highlightedElement?: string;
  tutorialProgress: Record<string, any>;
  userPreferences: Record<string, any>;
}

export interface TutorialStep {
  id: string;
  selector: string;
  title: string;
  description: string;
  highlightStyle?: {
    borderColor?: string;
    backgroundColor?: string;
    pulseAnimation?: boolean;
  };
}

export class TutorAIAgentClient {
  private config: AgUIClientConfig;
  private eventSource?: EventSource;
  private websocket?: WebSocket;
  private listeners: Map<string, ((event: AgentEvent) => void)[]> = new Map();
  private currentState: TutorAIState = {
    currentStep: 0,
    totalSteps: 0,
    tutorialProgress: {},
    userPreferences: {}
  };

  constructor(config: AgUIClientConfig) {
    this.config = config;
  }

  /**
   * Connect to AG-UI agent server
   */
  async connect(): Promise<void> {
    switch (this.config.transport) {
      case 'sse':
        await this.connectSSE();
        break;
      case 'websocket':
        await this.connectWebSocket();
        break;
      case 'polling':
        await this.connectPolling();
        break;
      default:
        throw new Error(`Unsupported transport: ${this.config.transport}`);
    }
  }

  /**
   * Server-Sent Events connection
   */
  private async connectSSE(): Promise<void> {
    const url = new URL('/api/ag-ui/stream', this.config.baseUrl);
    if (this.config.apiKey) {
      url.searchParams.set('apiKey', this.config.apiKey);
    }

    this.eventSource = new EventSource(url.toString());
    
    this.eventSource.onmessage = (event) => {
      try {
        const agentEvent: AgentEvent = JSON.parse(event.data);
        this.handleEvent(agentEvent);
      } catch (error) {
        console.error('Failed to parse AG-UI event:', error);
      }
    };

    this.eventSource.onerror = (error) => {
      console.error('AG-UI SSE connection error:', error);
      this.reconnect();
    };
  }

  /**
   * WebSocket connection
   */
  private async connectWebSocket(): Promise<void> {
    const wsUrl = this.config.baseUrl.replace('http', 'ws') + '/api/ag-ui/ws';
    this.websocket = new WebSocket(wsUrl);

    this.websocket.onmessage = (event) => {
      try {
        const agentEvent: AgentEvent = JSON.parse(event.data);
        this.handleEvent(agentEvent);
      } catch (error) {
        console.error('Failed to parse AG-UI WebSocket event:', error);
      }
    };

    this.websocket.onerror = (error) => {
      console.error('AG-UI WebSocket connection error:', error);
    };

    this.websocket.onclose = () => {
      this.reconnect();
    };
  }

  /**
   * Polling connection (fallback)
   */
  private async connectPolling(): Promise<void> {
    // Implementation for polling-based connection
    const poll = async () => {
      try {
        const response = await fetch(`${this.config.baseUrl}/api/ag-ui/poll`, {
          headers: this.config.apiKey ? { 'Authorization': `Bearer ${this.config.apiKey}` } : {}
        });
        
        if (response.ok) {
          const events: AgentEvent[] = await response.json();
          events.forEach(event => this.handleEvent(event));
        }
      } catch (error) {
        console.error('AG-UI polling error:', error);
      }
      
      setTimeout(poll, 1000); // Poll every second
    };

    poll();
  }

  /**
   * Handle incoming AG-UI events
   */
  private handleEvent(event: AgentEvent): void {
    // Update internal state based on event type
    this.updateState(event);

    // Notify listeners
    const eventListeners = this.listeners.get(event.type) || [];
    eventListeners.forEach(listener => listener(event));

    // Notify wildcard listeners
    const wildcardListeners = this.listeners.get('*') || [];
    wildcardListeners.forEach(listener => listener(event));
  }

  /**
   * Update internal state based on AG-UI events
   */
  private updateState(event: AgentEvent): void {
    switch (event.type) {
      case 'StateSnapshot':
        const snapshotEvent = event as StateSnapshotEvent;
        this.currentState = { ...this.currentState, ...snapshotEvent.snapshot };
        break;

      case 'StateDelta':
        const deltaEvent = event as StateDeltaEvent;
        // Apply JSON Patch operations
        deltaEvent.delta.forEach(operation => {
          this.applyJsonPatch(operation);
        });
        break;

      case 'StepStarted':
        const stepStartedEvent = event as StepStartedEvent;
        this.currentState.currentStep = parseInt(stepStartedEvent.stepName.replace('step-', ''));
        break;

      case 'RunStarted':
        // Reset state for new run
        this.currentState.currentStep = 0;
        break;
    }
  }

  /**
   * Apply JSON Patch operation to state
   */
  private applyJsonPatch(operation: any): void {
    // Simple JSON Patch implementation
    // In production, use a proper JSON Patch library
    const { op, path, value } = operation;
    const pathParts = path.split('/').filter(Boolean);
    
    let target = this.currentState as any;
    for (let i = 0; i < pathParts.length - 1; i++) {
      target = target[pathParts[i]];
    }
    
    const lastKey = pathParts[pathParts.length - 1];
    
    switch (op) {
      case 'replace':
      case 'add':
        target[lastKey] = value;
        break;
      case 'remove':
        delete target[lastKey];
        break;
    }
  }

  /**
   * Send message to agent
   */
  async sendMessage(message: string, context?: any): Promise<void> {
    const payload = {
      message,
      context,
      timestamp: new Date().toISOString()
    };

    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(payload));
    } else {
      // Fallback to HTTP POST
      await fetch(`${this.config.baseUrl}/api/ag-ui/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey ? { 'Authorization': `Bearer ${this.config.apiKey}` } : {})
        },
        body: JSON.stringify(payload)
      });
    }
  }

  /**
   * Request tutorial for current page
   */
  async requestTutorial(pageUrl: string, userQuery?: string): Promise<void> {
    await this.sendMessage('generate_tutorial', {
      pageUrl,
      userQuery,
      userPreferences: this.currentState.userPreferences
    });
  }

  /**
   * Request AI explanation for element
   */
  async explainElement(selector: string, question?: string): Promise<void> {
    await this.sendMessage('explain_element', {
      selector,
      question,
      pageContext: window.location.href
    });
  }

  /**
   * Add event listener
   */
  on(eventType: string, listener: (event: AgentEvent) => void): void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType)!.push(listener);
  }

  /**
   * Remove event listener
   */
  off(eventType: string, listener: (event: AgentEvent) => void): void {
    const eventListeners = this.listeners.get(eventType);
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  /**
   * Get current state
   */
  getState(): TutorAIState {
    return { ...this.currentState };
  }

  /**
   * Reconnect with exponential backoff
   */
  private async reconnect(): Promise<void> {
    let attempts = 0;
    const maxAttempts = this.config.reconnectAttempts;
    
    while (attempts < maxAttempts) {
      try {
        await new Promise(resolve => setTimeout(resolve, this.config.reconnectDelay * Math.pow(2, attempts)));
        await this.connect();
        console.log('AG-UI client reconnected successfully');
        return;
      } catch (error) {
        attempts++;
        console.error(`AG-UI reconnection attempt ${attempts} failed:`, error);
      }
    }
    
    console.error('AG-UI client failed to reconnect after maximum attempts');
  }

  /**
   * Disconnect from agent
   */
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = undefined;
    }
    
    if (this.websocket) {
      this.websocket.close();
      this.websocket = undefined;
    }
    
    this.listeners.clear();
  }
}

// Default configuration
export const defaultAgUIConfig: AgUIClientConfig = {
  baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  transport: 'sse',
  reconnectAttempts: 5,
  reconnectDelay: 1000
};

// Singleton instance
let agUIClient: TutorAIAgentClient | null = null;

export function getAgUIClient(): TutorAIAgentClient {
  if (!agUIClient) {
    agUIClient = new TutorAIAgentClient(defaultAgUIConfig);
  }
  return agUIClient;
}
