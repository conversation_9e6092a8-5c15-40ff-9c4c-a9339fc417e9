/**
 * AG-UI Server Implementation for TutorAI
 * Handles AI provider integration and event streaming
 */

import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { 
  AgentEvent,
  RunStartedEvent,
  RunFinishedEvent,
  RunErrorEvent,
  TextMessageStartEvent,
  TextMessageContentEvent,
  TextMessageEndEvent,
  ToolCallStartEvent,
  ToolCallArgsEvent,
  ToolCallEndEvent,
  ToolCallResultEvent,
  StateSnapshotEvent,
  StateDeltaEvent,
  StepStartedEvent,
  StepFinishedEvent
} from '@ag-ui/core';

export interface AIProvider {
  name: 'openai' | 'anthropic' | 'google';
  apiKey: string;
  model: string;
  baseUrl?: string;
}

export interface TutorialContext {
  pageUrl: string;
  pageTitle?: string;
  userQuery?: string;
  domElements?: Array<{
    selector: string;
    text: string;
    type: string;
  }>;
}

export interface AgUIServerConfig {
  aiProviders: AIProvider[];
  defaultProvider: 'openai' | 'anthropic' | 'google';
  maxTokens: number;
  temperature: number;
}

export class TutorAIAgentServer {
  private config: AgUIServerConfig;
  private openaiClient?: OpenAI;
  private anthropicClient?: Anthropic;
  private eventListeners: ((event: AgentEvent) => void)[] = [];

  constructor(config: AgUIServerConfig) {
    this.config = config;
    this.initializeProviders();
  }

  /**
   * Initialize AI provider clients
   */
  private initializeProviders(): void {
    const openaiProvider = this.config.aiProviders.find(p => p.name === 'openai');
    if (openaiProvider) {
      this.openaiClient = new OpenAI({
        apiKey: openaiProvider.apiKey,
        baseURL: openaiProvider.baseUrl
      });
    }

    const anthropicProvider = this.config.aiProviders.find(p => p.name === 'anthropic');
    if (anthropicProvider) {
      this.anthropicClient = new Anthropic({
        apiKey: anthropicProvider.apiKey
      });
    }
  }

  /**
   * Add event listener for AG-UI events
   */
  onEvent(listener: (event: AgentEvent) => void): void {
    this.eventListeners.push(listener);
  }

  /**
   * Emit AG-UI event to all listeners
   */
  private emitEvent(event: AgentEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in AG-UI event listener:', error);
      }
    });
  }

  /**
   * Generate tutorial steps for a webpage
   */
  async generateTutorial(context: TutorialContext, runId: string, threadId: string): Promise<void> {
    // Emit run started event
    this.emitEvent({
      type: 'RunStarted',
      runId,
      threadId,
      timestamp: new Date().toISOString()
    } as RunStartedEvent);

    try {
      // Step 1: Analyze page structure
      this.emitEvent({
        type: 'StepStarted',
        stepName: 'analyze_page',
        timestamp: new Date().toISOString()
      } as StepStartedEvent);

      const pageAnalysis = await this.analyzePageStructure(context);

      this.emitEvent({
        type: 'StepFinished',
        stepName: 'analyze_page',
        timestamp: new Date().toISOString()
      } as StepFinishedEvent);

      // Step 2: Generate tutorial content
      this.emitEvent({
        type: 'StepStarted',
        stepName: 'generate_content',
        timestamp: new Date().toISOString()
      } as StepStartedEvent);

      await this.generateTutorialContent(context, pageAnalysis, runId, threadId);

      this.emitEvent({
        type: 'StepFinished',
        stepName: 'generate_content',
        timestamp: new Date().toISOString()
      } as StepFinishedEvent);

      // Emit run finished event
      this.emitEvent({
        type: 'RunFinished',
        runId,
        threadId,
        result: { success: true },
        timestamp: new Date().toISOString()
      } as RunFinishedEvent);

    } catch (error) {
      console.error('Error generating tutorial:', error);
      this.emitEvent({
        type: 'RunError',
        message: error instanceof Error ? error.message : 'Unknown error',
        code: 'TUTORIAL_GENERATION_ERROR',
        timestamp: new Date().toISOString()
      } as RunErrorEvent);
    }
  }

  /**
   * Analyze page structure using AI
   */
  private async analyzePageStructure(context: TutorialContext): Promise<any> {
    const prompt = `Analyze this webpage structure and identify key interactive elements:
    
URL: ${context.pageUrl}
Title: ${context.pageTitle || 'Unknown'}
User Query: ${context.userQuery || 'General tutorial'}

DOM Elements:
${context.domElements?.map(el => `- ${el.type}: "${el.text}" (${el.selector})`).join('\n') || 'No elements provided'}

Please identify the most important elements for a tutorial and suggest a logical learning sequence.`;

    if (this.openaiClient) {
      const response = await this.openaiClient.chat.completions.create({
        model: this.config.aiProviders.find(p => p.name === 'openai')?.model || 'gpt-4-turbo-preview',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature
      });

      return response.choices[0]?.message?.content;
    }

    if (this.anthropicClient) {
      const response = await this.anthropicClient.messages.create({
        model: this.config.aiProviders.find(p => p.name === 'anthropic')?.model || 'claude-3-sonnet-20240229',
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        messages: [{ role: 'user', content: prompt }]
      });

      return response.content[0]?.type === 'text' ? response.content[0].text : '';
    }

    throw new Error('No AI provider available');
  }

  /**
   * Generate tutorial content with streaming
   */
  private async generateTutorialContent(
    context: TutorialContext, 
    pageAnalysis: string, 
    runId: string, 
    threadId: string
  ): Promise<void> {
    const messageId = `msg_${Date.now()}`;

    // Start text message
    this.emitEvent({
      type: 'TextMessageStart',
      messageId,
      role: 'assistant',
      timestamp: new Date().toISOString()
    } as TextMessageStartEvent);

    const prompt = `Based on the page analysis, create a step-by-step tutorial:

${pageAnalysis}

Create a comprehensive tutorial that guides the user through the most important features of this webpage. Format your response as a structured tutorial with clear steps.`;

    try {
      if (this.openaiClient) {
        const stream = await this.openaiClient.chat.completions.create({
          model: this.config.aiProviders.find(p => p.name === 'openai')?.model || 'gpt-4-turbo-preview',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          stream: true
        });

        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content;
          if (content) {
            this.emitEvent({
              type: 'TextMessageContent',
              messageId,
              delta: content,
              timestamp: new Date().toISOString()
            } as TextMessageContentEvent);
          }
        }
      } else if (this.anthropicClient) {
        const stream = await this.anthropicClient.messages.create({
          model: this.config.aiProviders.find(p => p.name === 'anthropic')?.model || 'claude-3-sonnet-20240229',
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          messages: [{ role: 'user', content: prompt }],
          stream: true
        });

        for await (const chunk of stream) {
          if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
            this.emitEvent({
              type: 'TextMessageContent',
              messageId,
              delta: chunk.delta.text,
              timestamp: new Date().toISOString()
            } as TextMessageContentEvent);
          }
        }
      }

      // End text message
      this.emitEvent({
        type: 'TextMessageEnd',
        messageId,
        timestamp: new Date().toISOString()
      } as TextMessageEndEvent);

    } catch (error) {
      console.error('Error streaming tutorial content:', error);
      throw error;
    }
  }

  /**
   * Explain a specific DOM element
   */
  async explainElement(
    selector: string, 
    question: string | undefined, 
    pageContext: string,
    runId: string,
    threadId: string
  ): Promise<void> {
    this.emitEvent({
      type: 'RunStarted',
      runId,
      threadId,
      timestamp: new Date().toISOString()
    } as RunStartedEvent);

    try {
      const messageId = `msg_${Date.now()}`;

      // Start text message
      this.emitEvent({
        type: 'TextMessageStart',
        messageId,
        role: 'assistant',
        timestamp: new Date().toISOString()
      } as TextMessageStartEvent);

      const prompt = question 
        ? `User is asking about element "${selector}" on page ${pageContext}: "${question}". Please provide a helpful explanation.`
        : `Please explain what the element "${selector}" does on page ${pageContext} and how the user can interact with it.`;

      // Stream explanation
      await this.streamAIResponse(prompt, messageId);

      // End text message
      this.emitEvent({
        type: 'TextMessageEnd',
        messageId,
        timestamp: new Date().toISOString()
      } as TextMessageEndEvent);

      // Emit tool call for highlighting
      await this.emitHighlightTool(selector, runId, threadId);

      this.emitEvent({
        type: 'RunFinished',
        runId,
        threadId,
        result: { success: true },
        timestamp: new Date().toISOString()
      } as RunFinishedEvent);

    } catch (error) {
      console.error('Error explaining element:', error);
      this.emitEvent({
        type: 'RunError',
        message: error instanceof Error ? error.message : 'Unknown error',
        code: 'ELEMENT_EXPLANATION_ERROR',
        timestamp: new Date().toISOString()
      } as RunErrorEvent);
    }
  }

  /**
   * Stream AI response content
   */
  private async streamAIResponse(prompt: string, messageId: string): Promise<void> {
    if (this.openaiClient) {
      const stream = await this.openaiClient.chat.completions.create({
        model: this.config.aiProviders.find(p => p.name === 'openai')?.model || 'gpt-4-turbo-preview',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        stream: true
      });

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          this.emitEvent({
            type: 'TextMessageContent',
            messageId,
            delta: content,
            timestamp: new Date().toISOString()
          } as TextMessageContentEvent);
        }
      }
    } else if (this.anthropicClient) {
      const stream = await this.anthropicClient.messages.create({
        model: this.config.aiProviders.find(p => p.name === 'anthropic')?.model || 'claude-3-sonnet-20240229',
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        messages: [{ role: 'user', content: prompt }],
        stream: true
      });

      for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
          this.emitEvent({
            type: 'TextMessageContent',
            messageId,
            delta: chunk.delta.text,
            timestamp: new Date().toISOString()
          } as TextMessageContentEvent);
        }
      }
    }
  }

  /**
   * Emit tool call for element highlighting
   */
  private async emitHighlightTool(selector: string, runId: string, threadId: string): Promise<void> {
    const toolCallId = `tool_${Date.now()}`;

    // Start tool call
    this.emitEvent({
      type: 'ToolCallStart',
      toolCallId,
      toolCallName: 'highlight_element',
      timestamp: new Date().toISOString()
    } as ToolCallStartEvent);

    // Stream tool arguments
    const args = JSON.stringify({ selector, style: { borderColor: '#3b82f6', pulseAnimation: true } });
    this.emitEvent({
      type: 'ToolCallArgs',
      toolCallId,
      delta: args,
      timestamp: new Date().toISOString()
    } as ToolCallArgsEvent);

    // End tool call
    this.emitEvent({
      type: 'ToolCallEnd',
      toolCallId,
      timestamp: new Date().toISOString()
    } as ToolCallEndEvent);

    // Tool result
    this.emitEvent({
      type: 'ToolCallResult',
      messageId: `msg_${Date.now()}`,
      toolCallId,
      content: `Element ${selector} highlighted successfully`,
      role: 'tool',
      timestamp: new Date().toISOString()
    } as ToolCallResultEvent);
  }

  /**
   * Update tutorial state
   */
  updateState(state: any): void {
    this.emitEvent({
      type: 'StateSnapshot',
      snapshot: state,
      timestamp: new Date().toISOString()
    } as StateSnapshotEvent);
  }

  /**
   * Apply state delta
   */
  applyStateDelta(operations: any[]): void {
    this.emitEvent({
      type: 'StateDelta',
      delta: operations,
      timestamp: new Date().toISOString()
    } as StateDeltaEvent);
  }
}

// Default server configuration
export const defaultAgUIServerConfig: AgUIServerConfig = {
  aiProviders: [
    {
      name: 'openai',
      apiKey: process.env.OPENAI_API_KEY || '',
      model: 'gpt-4-turbo-preview'
    },
    {
      name: 'anthropic',
      apiKey: process.env.ANTHROPIC_API_KEY || '',
      model: 'claude-3-sonnet-20240229'
    }
  ].filter(provider => provider.apiKey), // Only include providers with API keys
  defaultProvider: 'openai',
  maxTokens: 2000,
  temperature: 0.7
};

// Singleton instance
let agUIServer: TutorAIAgentServer | null = null;

export function getAgUIServer(): TutorAIAgentServer {
  if (!agUIServer) {
    agUIServer = new TutorAIAgentServer(defaultAgUIServerConfig);
  }
  return agUIServer;
}
