{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "db:check": "npx tsx src/lib/db-check.ts", "db:setup": "prisma generate && prisma migrate deploy", "db:init": "npx tsx src/lib/db-init.ts", "db:reset": "prisma migrate reset --force"}, "dependencies": {"@ag-ui/core": "^0.1.0", "@ag-ui/client": "^0.1.0", "@ag-ui/encoder": "^0.1.0", "@anthropic-ai/sdk": "^0.24.3", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@types/bcryptjs": "^2.4.6", "autoprefixer": "10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.23.0", "lucide-react": "^0.468.0", "next": "14.2.23", "next-auth": "^4.24.11", "next-themes": "^0.2.1", "openai": "^4.52.7", "prettier": "^3.3.3", "prisma": "^6.11.1", "radix-ui": "^1.1.3", "react": "^18", "react-day-picker": "^9.5.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "recharts": "^3.0.2", "stripe": "^17.6.0", "tempo-devtools": "^2.0.108", "vaul": "^1.1.2", "zod": "^4.0.5"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "typescript": "^5", "tsx": "^4.7.0"}}